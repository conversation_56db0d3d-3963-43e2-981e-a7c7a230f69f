# 📊 舆情监控系统 - 完整数据源地址列表

## 🌐 基础API地址

### 主服务器
**基础URL**: `https://new.0407123.xyz/api/s?id={source_id}`

### 备用服务器  
**基础URL**: `https://newsnow.busiyi.world/api/s?id={source_id}`

---

## 💰 金融类数据源 (17个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| mktnews | MKTNews | `https://new.0407123.xyz/api/s?id=mktnews` | `https://newsnow.busiyi.world/api/s?id=mktnews` | 2 |
| mktnews-flash | MKTNews快讯 | `https://new.0407123.xyz/api/s?id=mktnews-flash` | `https://newsnow.busiyi.world/api/s?id=mktnews-flash` | 2 |
| wallstreetcn | 华尔街见闻 | `https://new.0407123.xyz/api/s?id=wallstreetcn` | `https://newsnow.busiyi.world/api/s?id=wallstreetcn` | 1 |
| wallstreetcn-quick | 华尔街见闻快讯 | `https://new.0407123.xyz/api/s?id=wallstreetcn-quick` | `https://newsnow.busiyi.world/api/s?id=wallstreetcn-quick` | 1 |
| wallstreetcn-news | 华尔街见闻新闻 | `https://new.0407123.xyz/api/s?id=wallstreetcn-news` | `https://newsnow.busiyi.world/api/s?id=wallstreetcn-news` | 1 |
| wallstreetcn-hot | 华尔街见闻热点 | `https://new.0407123.xyz/api/s?id=wallstreetcn-hot` | `https://newsnow.busiyi.world/api/s?id=wallstreetcn-hot` | 1 |
| cls | 财联社 | `https://new.0407123.xyz/api/s?id=cls` | `https://newsnow.busiyi.world/api/s?id=cls` | 1 |
| cls-telegraph | 财联社电报 | `https://new.0407123.xyz/api/s?id=cls-telegraph` | `https://newsnow.busiyi.world/api/s?id=cls-telegraph` | 1 |
| cls-depth | 财联社深度 | `https://new.0407123.xyz/api/s?id=cls-depth` | `https://newsnow.busiyi.world/api/s?id=cls-depth` | 1 |
| cls-hot | 财联社热点 | `https://new.0407123.xyz/api/s?id=cls-hot` | `https://newsnow.busiyi.world/api/s?id=cls-hot` | 1 |
| xueqiu | 雪球 | `https://new.0407123.xyz/api/s?id=xueqiu` | `https://newsnow.busiyi.world/api/s?id=xueqiu` | 1 |
| xueqiu-hotstock | 雪球热股 | `https://new.0407123.xyz/api/s?id=xueqiu-hotstock` | `https://newsnow.busiyi.world/api/s?id=xueqiu-hotstock` | 1 |
| gelonghui | 格隆汇 | `https://new.0407123.xyz/api/s?id=gelonghui` | `https://newsnow.busiyi.world/api/s?id=gelonghui` | 2 |
| fastbull | 法布财经 | `https://new.0407123.xyz/api/s?id=fastbull` | `https://newsnow.busiyi.world/api/s?id=fastbull` | 2 |
| fastbull-express | 法布财经快讯 | `https://new.0407123.xyz/api/s?id=fastbull-express` | `https://newsnow.busiyi.world/api/s?id=fastbull-express` | 2 |
| fastbull-news | 法布财经新闻 | `https://new.0407123.xyz/api/s?id=fastbull-news` | `https://newsnow.busiyi.world/api/s?id=fastbull-news` | 2 |
| jin10 | 金十数据 | `https://new.0407123.xyz/api/s?id=jin10` | `https://newsnow.busiyi.world/api/s?id=jin10` | 1 |

---

## 💻 科技类数据源 (19个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| v2ex | V2EX | `https://new.0407123.xyz/api/s?id=v2ex` | `https://newsnow.busiyi.world/api/s?id=v2ex` | 3 |
| v2ex-share | V2EX分享 | `https://new.0407123.xyz/api/s?id=v2ex-share` | `https://newsnow.busiyi.world/api/s?id=v2ex-share` | 1 |
| coolapk | 酷安 | `https://new.0407123.xyz/api/s?id=coolapk` | `https://newsnow.busiyi.world/api/s?id=coolapk` | 2 |
| 36kr | 36氪 | `https://new.0407123.xyz/api/s?id=36kr` | `https://newsnow.busiyi.world/api/s?id=36kr` | 1 |
| 36kr-quick | 36氪快讯 | `https://new.0407123.xyz/api/s?id=36kr-quick` | `https://newsnow.busiyi.world/api/s?id=36kr-quick` | 1 |
| ithome | IT之家 | `https://new.0407123.xyz/api/s?id=ithome` | `https://newsnow.busiyi.world/api/s?id=ithome` | 1 |
| pcbeta | 远景论坛 | `https://new.0407123.xyz/api/s?id=pcbeta` | `https://newsnow.busiyi.world/api/s?id=pcbeta` | 2 |
| pcbeta-windows11 | 远景论坛Win11 | `https://new.0407123.xyz/api/s?id=pcbeta-windows11` | `https://newsnow.busiyi.world/api/s?id=pcbeta-windows11` | 2 |
| solidot | Solidot | `https://new.0407123.xyz/api/s?id=solidot` | `https://newsnow.busiyi.world/api/s?id=solidot` | 2 |
| hackernews | Hacker News | `https://new.0407123.xyz/api/s?id=hackernews` | `https://newsnow.busiyi.world/api/s?id=hackernews` | 2 |
| producthunt | Product Hunt | `https://new.0407123.xyz/api/s?id=producthunt` | `https://newsnow.busiyi.world/api/s?id=producthunt` | 2 |
| github | Github | `https://new.0407123.xyz/api/s?id=github` | `https://newsnow.busiyi.world/api/s?id=github` | 2 |
| github-trending-today | Github今日趋势 | `https://new.0407123.xyz/api/s?id=github-trending-today` | `https://newsnow.busiyi.world/api/s?id=github-trending-today` | 2 |
| nowcoder | 牛客 | `https://new.0407123.xyz/api/s?id=nowcoder` | `https://newsnow.busiyi.world/api/s?id=nowcoder` | 2 |
| sspai | 少数派 | `https://new.0407123.xyz/api/s?id=sspai` | `https://newsnow.busiyi.world/api/s?id=sspai` | 2 |
| juejin | 稀土掘金 | `https://new.0407123.xyz/api/s?id=juejin` | `https://newsnow.busiyi.world/api/s?id=juejin` | 2 |
| chongbuluo | 虫部落 | `https://new.0407123.xyz/api/s?id=chongbuluo` | `https://newsnow.busiyi.world/api/s?id=chongbuluo` | 2 |
| chongbuluo-latest | 虫部落最新 | `https://new.0407123.xyz/api/s?id=chongbuluo-latest` | `https://newsnow.busiyi.world/api/s?id=chongbuluo-latest` | 2 |
| chongbuluo-hot | 虫部落热门 | `https://new.0407123.xyz/api/s?id=chongbuluo-hot` | `https://newsnow.busiyi.world/api/s?id=chongbuluo-hot` | 2 |

---

## 📰 新闻类数据源 (7个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| zaobao | 联合早报 | `https://new.0407123.xyz/api/s?id=zaobao` | `https://newsnow.busiyi.world/api/s?id=zaobao` | 1 |
| toutiao | 今日头条 | `https://new.0407123.xyz/api/s?id=toutiao` | `https://newsnow.busiyi.world/api/s?id=toutiao` | 1 |
| thepaper | 澎湃新闻 | `https://new.0407123.xyz/api/s?id=thepaper` | `https://newsnow.busiyi.world/api/s?id=thepaper` | 1 |
| sputniknewscn | 卫星通讯社 | `https://new.0407123.xyz/api/s?id=sputniknewscn` | `https://newsnow.busiyi.world/api/s?id=sputniknewscn` | 2 |
| cankaoxiaoxi | 参考消息 | `https://new.0407123.xyz/api/s?id=cankaoxiaoxi` | `https://newsnow.busiyi.world/api/s?id=cankaoxiaoxi` | 2 |
| kaopu | 靠谱新闻 | `https://new.0407123.xyz/api/s?id=kaopu` | `https://newsnow.busiyi.world/api/s?id=kaopu` | 2 |
| ifeng | 凤凰网 | `https://new.0407123.xyz/api/s?id=ifeng` | `https://newsnow.busiyi.world/api/s?id=ifeng` | 2 |

---

## 👥 社交类数据源 (6个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| zhihu | 知乎 | `https://new.0407123.xyz/api/s?id=zhihu` | `https://newsnow.busiyi.world/api/s?id=zhihu` | 1 |
| weibo | 微博 | `https://new.0407123.xyz/api/s?id=weibo` | `https://newsnow.busiyi.world/api/s?id=weibo` | 1 |
| douyin | 抖音 | `https://new.0407123.xyz/api/s?id=douyin` | `https://newsnow.busiyi.world/api/s?id=douyin` | 2 |
| tieba | 百度贴吧 | `https://new.0407123.xyz/api/s?id=tieba` | `https://newsnow.busiyi.world/api/s?id=tieba` | 2 |
| baidu | 百度热搜 | `https://new.0407123.xyz/api/s?id=baidu` | `https://newsnow.busiyi.world/api/s?id=baidu` | 1 |

---

## 🎮 娱乐类数据源 (5个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| bilibili | 哔哩哔哩 | `https://new.0407123.xyz/api/s?id=bilibili` | `https://newsnow.busiyi.world/api/s?id=bilibili` | 1 |
| bilibili-hot-search | 哔哩哔哩热搜 | `https://new.0407123.xyz/api/s?id=bilibili-hot-search` | `https://newsnow.busiyi.world/api/s?id=bilibili-hot-search` | 1 |
| bilibili-hot-video | 哔哩哔哩热门视频 | `https://new.0407123.xyz/api/s?id=bilibili-hot-video` | `https://newsnow.busiyi.world/api/s?id=bilibili-hot-video` | 1 |
| bilibili-ranking | 哔哩哔哩排行榜 | `https://new.0407123.xyz/api/s?id=bilibili-ranking` | `https://newsnow.busiyi.world/api/s?id=bilibili-ranking` | 1 |
| kuaishou | 快手 | `https://new.0407123.xyz/api/s?id=kuaishou` | `https://newsnow.busiyi.world/api/s?id=kuaishou` | 2 |

---

## ⚽ 体育类数据源 (1个)

| 数据源ID | 数据源名称 | 主地址 | 备用地址 | 优先级 |
|---------|-----------|--------|----------|--------|
| hupu | 虎扑 | `https://new.0407123.xyz/api/s?id=hupu` | `https://newsnow.busiyi.world/api/s?id=hupu` | 2 |

---

## 📊 统计信息

- **总计数据源**: 55个
- **启用状态**: 全部启用 (55/55)
- **主服务器**: `https://new.0407123.xyz`
- **备用服务器**: `https://newsnow.busiyi.world`
- **API路径**: `/api/s?id={source_id}`

## 🔧 使用方法

### 直接访问示例
```bash
# 访问知乎数据
curl "https://new.0407123.xyz/api/s?id=zhihu"

# 访问备用服务器
curl "https://newsnow.busiyi.world/api/s?id=zhihu"
```

### Python调用示例
```python
import requests

def fetch_data(source_id):
    primary_url = f"https://new.0407123.xyz/api/s?id={source_id}"
    backup_url = f"https://newsnow.busiyi.world/api/s?id={source_id}"
    
    try:
        response = requests.get(primary_url, timeout=10)
        return response.json()
    except:
        response = requests.get(backup_url, timeout=10)
        return response.json()

# 使用示例
data = fetch_data("zhihu")
print(data)
```

## 📝 文件信息

- **文件名称**: 数据源地址列表.md
- **创建时间**: 2025-08-18
- **数据源总数**: 55个
- **分类**: 金融(17)、科技(19)、新闻(7)、社交(6)、娱乐(5)、体育(1)
- **更新频率**: 根据配置文件更新

所有数据源都支持主备自动切换，确保数据采集的高可用性。

---

*最后更新: 2025-08-18*